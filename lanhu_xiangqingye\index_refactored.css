/* 重构后的CSS样式 - 使用现代CSS技术和语义化命名 */

/* 页面基础样式 */
.page {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  font-family: 'AlibabaPuHuiTi-Regular', 'PingFangSC-Regular', sans-serif;
}

.main-content {
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

/* 页面头部导航 */
.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 23px 240px;
  background-color: #ffffff;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-image: url(./img/e6efb85391a946f2a138254723f94e8a_mergeImage.png);
  background-size: cover;
  border: 1px solid rgba(151, 151, 151, 1);
}

.company-name {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
  margin: 0;
  line-height: 42px;
}

.main-navigation {
  display: flex;
  align-items: center;
  gap: 40px;
  background-color: #ffffff;
  padding: 34px 266px 34px 0;
}

.nav-link {
  font-size: 16px;
  color: #000000;
  text-decoration: none;
  line-height: 22px;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #0e6be4;
}

.nav-icon {
  width: 14px;
  height: 14px;
}

/* 产品导航栏 */
.product-navigation {
  display: flex;
  align-items: center;
  background-color: #090909;
  padding: 0 460px;
  height: 92px;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 92px;
  padding: 0 32px;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background-color: #0e6be4;
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
}

.contact-sales {
  background-color: #ffffff;
  color: #000000;
  margin-left: auto;
  margin-right: 447px;
  padding: 0 26px;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.contact-sales:hover {
  background-color: #f5f5f5;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 13px 0 0 460px;
}

.breadcrumb-item {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 22px;
}

.breadcrumb-current {
  font-size: 14px;
  color: #0055c3;
  font-weight: 500;
  line-height: 22px;
}

.breadcrumb-icon {
  width: 18px;
  height: 18px;
}

/* 产品展示区域 */
.product-showcase {
  margin: 11px 0 0 240px;
  width: 1440px;
  height: 382px;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
}

.showcase-tabs {
  width: 100%;
  height: 100%;
  background-image: url(./img/b8b7a955f3a14cf9a0e61eefe0bc06a3_mergeImage.png);
  background-size: cover;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 40px;
  padding-bottom: 15px;
}

.tab-button {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.tab-button.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

/* 产品介绍 */
.product-intro {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 38px 460px 0 0;
  gap: 16px;
}

.intro-highlight {
  width: 72px;
  height: 3px;
  background-color: #0055c3;
}

.intro-description {
  width: 871px;
  font-size: 20px;
  font-weight: 300;
  color: #000000;
  line-height: 27px;
  text-align: right;
  margin: 0;
}

.contact-expert {
  font-size: 20px;
  color: #ec2914;
  text-decoration: none;
  line-height: 27px;
  transition: color 0.3s ease;
}

.contact-expert:hover {
  color: #d41e0f;
}

/* 产品特性详情 */
.product-features {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  width: 1030px;
  margin: 42px 0 0 445px;
  padding: 64px;
}

.features-title {
  font-size: 36px;
  color: #000000;
  margin: 0 0 14px 0;
  line-height: 50px;
}

.features-subtitle {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
  margin: 0 0 24px 0;
  line-height: 24px;
}

.feature-item {
  margin-bottom: 24px;
}

.feature-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
  margin: 0 0 8px 0;
  line-height: 24px;
}

.feature-description {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.67);
  line-height: 24px;
  margin: 0;
}

/* 产品优势 */
.product-advantages {
  margin: 57px 0 0 445px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 23px;
}

.section-icon {
  width: 30px;
  height: 30px;
}

.section-title {
  font-size: 36px;
  color: #000000;
  margin: 0;
  line-height: 50px;
}

.advantages-list {
  background-image: url(./img/da36ba129190407d9f85de78f5f3456d_mergeImage.png);
  background-size: cover;
  border-radius: 8px;
  width: 1030px;
  height: 330px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 603px 0 0;
  gap: 16px;
}

.advantage-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.advantage-bullet {
  width: 8px;
  height: 8px;
  background-color: #000000;
  border-radius: 50%;
}

.advantage-text {
  font-size: 16px;
  color: #000000;
  line-height: 31px;
}

/* 合作共赢 */
.cooperation {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
  width: 1030px;
  height: 169px;
  margin: 20px 0 0 445px;
  padding: 26px 50px;
}

.cooperation-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cooperation-title {
  font-size: 20px;
  color: #000000;
  margin: 0;
  line-height: 27px;
}

.cooperation-description {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.67);
  line-height: 24px;
  margin: 16px 0 0 0;
}

/* 产品应用 */
.product-applications {
  margin: 57px 0 0 444px;
}

.applications-grid {
  background-color: #ffffff;
  border-radius: 8px;
  width: 1030px;
  height: 194px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  align-items: center;
  padding: 0 60px;
  gap: 40px;
}

.application-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.application-icon {
  width: 8px;
  height: 8px;
  background-color: #000000;
  border-radius: 50%;
}

.application-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.application-title {
  font-size: 16px;
  color: #000000;
  margin: 0;
  line-height: 31px;
}

.application-subtitle {
  font-size: 16px;
  color: #000000;
  margin: 0;
  line-height: 31px;
}

/* 联系我们 */
.contact-us {
  width: 1030px;
  height: 170px;
  margin: 60px 0 0 445px;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png) no-repeat;
  background-size: cover;
  padding: 26px 50px 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.contact-header {
  margin-bottom: 3px;
}

.contact-title {
  font-size: 24px;
  color: #000000;
  margin: 0;
  line-height: 33px;
}

.contact-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.contact-description {
  width: 639px;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  margin: 0;
}

.contact-button {
  background-color: #ec2914;
  width: 100px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.contact-button:hover {
  background-color: #d41e0f;
}

.button-text {
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
}

/* 页脚 */
.site-footer {
  width: 1030px;
  height: 230px;
  margin: 60px 0 0 445px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png) no-repeat;
  background-size: cover;
  padding: 18px 60px;
  position: relative;
}

.footer-links {
  display: flex;
  gap: 110px;
  margin-bottom: 30px;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-title {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 22px;
}

.footer-link {
  font-size: 16px;
  color: #ffffff;
  text-decoration: none;
  line-height: 22px;
  transition: opacity 0.3s ease;
}

.footer-link:hover {
  opacity: 0.8;
}

.footer-brand {
  position: absolute;
  left: 20px;
  bottom: 3px;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  width: 56px;
  height: 55px;
}

.brand-name {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
  line-height: 42px;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .main-content {
    max-width: 100%;
    padding: 0 20px;
  }
  
  .site-header {
    padding: 20px;
  }
  
  .product-navigation {
    padding: 0 20px;
  }
  
  .product-showcase,
  .product-features,
  .product-advantages,
  .cooperation,
  .product-applications,
  .contact-us,
  .site-footer {
    width: 100%;
    max-width: 1030px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .site-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .main-navigation {
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px 0;
  }
  
  .product-navigation {
    flex-direction: column;
    height: auto;
    padding: 20px;
  }
  
  .nav-item {
    height: 50px;
    width: 100%;
  }
  
  .contact-sales {
    margin: 10px 0 0 0;
    width: 100%;
  }
  
  .breadcrumb {
    padding: 20px;
    flex-wrap: wrap;
  }
  
  .product-intro {
    padding: 20px;
    align-items: center;
    text-align: center;
  }
  
  .intro-description {
    width: 100%;
    text-align: center;
  }
  
  .product-features {
    padding: 30px 20px;
  }
  
  .advantages-list {
    padding: 30px 20px;
    height: auto;
  }
  
  .cooperation {
    padding: 20px;
    height: auto;
  }
  
  .applications-grid {
    flex-direction: column;
    height: auto;
    padding: 30px 20px;
  }
  
  .contact-us {
    padding: 20px;
    height: auto;
  }
  
  .contact-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .site-footer {
    padding: 20px;
    height: auto;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 20px;
  }
  
  .footer-brand {
    position: static;
    margin-top: 20px;
  }
}
