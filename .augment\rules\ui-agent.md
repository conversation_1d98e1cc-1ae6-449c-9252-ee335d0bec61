---
type: "manual"
description: "globs:"
---
# HTML静态页面构建规则

## 使用场景
- 根据用户提供的UI图、HTML、CSS在指定页面添加静态UI界面
- 开发新的HTML页面或组件的布局和样式
- 需要将外部UI资源转换为项目内部统一规范的场景

## 关键规则
- **严守视觉还原**：严格按照UI图或参考代码进行视觉还原，但应以实现布局意图为核心。
- **禁止机械复刻**：**严禁**机械式地复制UI设计工具（如蓝湖）生成的DOM结构和布局代码（如 `margin`, `position`）。
- **理解并重构**：必须分析并理解设计的DOM结构和布局意图（如居中、两端对齐等），并使用语义化标签和 `Flexbox` 等现代CSS技术进行重构。
- **语义化命名**：在理解和重构布局后，必须对CSS类名进行有意义的命名，以反映其功能或内容，避免使用如 `box_1`、`group_2` 等无意义名称。
- **统一单位规范**：使用标准的CSS单位（px、em、rem、%等），根据具体需求选择合适的单位。
- **代码结构清晰**：采用语义化HTML标签，样式使用CSS或SCSS，保持代码结构清晰易维护。

## 详细指南

### 单位使用规则
- 使用px作为基础像素单位
- 使用rem或em用于字体大小，实现更好的响应式效果
- 使用%或vw/vh用于响应式布局
- 根据设计需求选择最合适的单位，无需强制转换

### 代码组织结构
- HTML结构使用标准的HTML5语义化标签
- CSS样式可以内联、内部样式表或外部样式表的形式组织
- 推荐使用外部CSS文件，便于维护和复用

### 布局实现原则
- **意图驱动开发**：在编码前，必须分析UI元素间的对齐与分布关系。UI工具导出的代码仅作为视觉与尺寸参考，其布局实现方式（通常是 `margin` 或 `position`）必须被更优的方式替代。
- **Flexbox优先**：对于一维布局（元素沿水平或垂直方向排列），**必须**优先使用Flexbox。通过 `justify-content`, `align-items`, 和 `gap` 属性控制主轴和交叉轴的对齐与间距。
- **Grid布局**：对于二维布局（行列网格），推荐使用CSS Grid布局，提供更强大的布局控制能力。
- **合理使用Margin**：`margin` 属性应主要用于元素与元素之间的微调间距，**严禁**使用大数值的 `margin` 来实现页面宏观布局定位。
- **复杂布局**：对于复杂的界面，应通过嵌套的Flexbox/Grid容器来构建，以保持布局的清晰和可扩展性。

### 视觉还原原则
- 保持边距、内边距、字体大小、行高等与UI图一致
- 保持颜色、阴影、圆角等视觉效果与UI图一致
- 响应式布局应根据原始设计适配，使用媒体查询等技术

### 浏览器兼容性
- 确保代码在主流浏览器中正常显示
- 使用CSS前缀处理兼容性问题
- 考虑移动端和桌面端的适配

## 示例
<example>
<!-- 正确的HTML页面布局实现 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品卡片</title>
</head>
<body>
  <div class="product-card">
    <div class="product-image">
      <img src="/static/image/product.png" alt="产品图片">
    </div>
    <div class="product-info">
      <h3 class="product-title">产品名称</h3>
      <div class="product-price">¥99.00</div>
      <div class="product-actions">
        <button class="buy-btn">立即购买</button>
      </div>
    </div>
  </div>
</body>
</html>

<style>
.product-card {
  width: 320px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.product-image {
  height: 200px;
  margin-bottom: 12px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.product-title {
  font-size: 16px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.product-price {
  font-size: 18px;
  color: #ff6700;
  margin-bottom: 12px;
  font-weight: bold;
}

.buy-btn {
  width: 100%;
  height: 40px;
  background-color: #ff6700;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.buy-btn:hover {
  background-color: #e55a00;
}
</style>
</example>

<example type="invalid">
<!-- 错误的HTML页面布局实现 -->
<!DOCTYPE html>
<html>
<head>
  <title>错误示例</title>
</head>
<body>
  <div class="c1">
    <div class="c2">
      <img class="c3" src="/static/image/product.png">
    </div>
    <div class="c4">
      <h3 class="c5">产品名称</h3>
      <div class="c6">¥99.00</div>
      <div class="c7">
        <button class="c8">立即购买</button>
      </div>
    </div>
  </div>
</body>
</html>

<style>
.c1 {
  width: 320px;
  padding: 16px;
  /* 错误：使用无意义的类名 */
}
.c2 {
  height: 200px;
  /* 错误：缺少语义化命名 */
}
.c3 {
  width: 100%;
  /* 错误：没有考虑图片适配 */
}
.c5 {
  font-size: 1rem;
  /* 错误：缺少合适的字体样式 */
}
.c6 {
  font-size: 18px;
  /* 错误：缺少颜色和字重设置 */
}
.c8 {
  width: 100%;
  height: 40px;
  background-color: #ff6700;
  color: #fff;
  /* 错误：缺少交互状态和过渡效果 */
}
</style>
</example>

## 注意事项
- 优先使用语义化HTML标签，提高代码可读性和可访问性
- 合理使用CSS单位，根据具体场景选择px、rem、em、%等
- 注意浏览器兼容性，特别是移动端适配
- 保持代码结构清晰，便于后续维护和扩展
- 使用现代CSS特性如Flexbox、Grid等提高布局效率

