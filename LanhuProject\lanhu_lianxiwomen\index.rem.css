html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 56.054rem;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 56.054rem;
}

.box_15 {
  width: 38.747rem;
  height: 2.454rem;
}

.section_1 {
  border-radius: 50%;
  background-image: url(./img/3a51bdb8986a4010a237ffc821e616ff_mergeImage.png);
  width: 1.12rem;
  height: 1.12rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.614rem;
}

.text_1 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.56rem 0 0 0.32rem;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 33.067rem;
  height: 2.454rem;
  margin-left: 1.094rem;
}

.text_2 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 7.094rem;
}

.text_3 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.04rem;
}

.text_4 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_5 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.894rem 0 1.067rem;
}

.box_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 39.414rem;
  height: 2.454rem;
  margin-left: 11.787rem;
}

.text_7 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text_8 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.707rem;
}

.text_9 {
  width: 0.96rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.707rem;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.2rem;
}

.text_10 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.827rem 0 0 0.694rem;
}

.box_16 {
  width: 4.534rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 12.267rem;
}

.text_11 {
  width: 2.24rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_2 {
  width: 0.48rem;
  height: 0.48rem;
  margin: 0.027rem 0 0 0.16rem;
}

.text_12 {
  width: 1.494rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 0.16rem;
}

.box_4 {
  background-color: rgba(0, 0, 0, 1);
  width: 51.2rem;
  height: 5.334rem;
  margin-top: 0.32rem;
}

.group_8 {
  width: 23.04rem;
  height: 2.72rem;
  margin: 0.987rem 0 0 12.267rem;
}

.image_1 {
  width: 1.867rem;
  height: 1.84rem;
}

.text-wrapper_15 {
  width: 20.64rem;
  height: 2.72rem;
}

.text_13 {
  width: 3.2rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.8rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text_14 {
  width: 20.64rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.667rem;
  margin-top: 0.267rem;
}

.box_6 {
  background-color: rgba(255, 67, 0, 1);
  width: 27.467rem;
  height: 0.107rem;
  margin: 1.52rem 0 0 11.867rem;
}

.text-wrapper_3 {
  background-color: rgba(14, 107, 228, 1);
  border-radius: 0px 0px 25px 25px;
  height: 1.334rem;
  width: 27.467rem;
  margin: 1.067rem 0 0 11.867rem;
}

.text_15 {
  width: 2.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.24rem 0 0 1.6rem;
}

.box_17 {
  width: 27.467rem;
  height: 20.8rem;
  margin: 4.48rem 0 0 11.867rem;
}

.group_2 {
  width: 0.027rem;
  height: 20.8rem;
  border: 1px solid rgba(0, 0, 0, 1);
}

.box_18 {
  width: 17.814rem;
  height: 18.8rem;
  margin: 1.307rem 0 0 4.8rem;
}

.box_19 {
  width: 9.68rem;
  height: 0.534rem;
  margin-left: 0.267rem;
}

.text-wrapper_4 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_16 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_17 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text-wrapper_5 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_18 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_19 {
  width: 0.64rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-wrapper_3 {
  width: 17.814rem;
  height: 0.907rem;
  margin-top: 0.027rem;
}

.image_2 {
  width: 8.774rem;
  height: 0.907rem;
}

.image_3 {
  width: 8.774rem;
  height: 0.907rem;
}

.text_20 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.image-text_11 {
  width: 17.814rem;
  height: 1.947rem;
  margin-top: 0.027rem;
}

.image_4 {
  width: 17.814rem;
  height: 0.907rem;
}

.text-group_1 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.text_21 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_22 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-text_12 {
  width: 8.774rem;
  height: 1.947rem;
  margin-top: 0.027rem;
}

.image_5 {
  width: 8.774rem;
  height: 0.907rem;
}

.text-group_2 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.text_23 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_24 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-text_13 {
  width: 17.814rem;
  height: 1.947rem;
  margin-top: 0.027rem;
}

.image_6 {
  width: 17.814rem;
  height: 0.907rem;
}

.text-group_11 {
  width: 10.054rem;
  height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.text-wrapper_6 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_25 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_26 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text-wrapper_7 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_27 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_28 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-wrapper_4 {
  width: 17.814rem;
  height: 0.907rem;
  margin-top: 0.027rem;
}

.image_7 {
  width: 8.774rem;
  height: 0.907rem;
}

.image_8 {
  width: 8.774rem;
  height: 0.907rem;
}

.text-wrapper_8 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.text_29 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_30 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.image-text_14 {
  width: 17.814rem;
  height: 1.947rem;
  margin-top: 0.027rem;
}

.box_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 17.814rem;
  height: 0.907rem;
  border: 1px solid rgba(211, 211, 211, 1);
}

.text_31 {
  width: 3.307rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(211, 211, 211, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.16rem 0 0 0.32rem;
}

.thumbnail_3 {
  width: 0.267rem;
  height: 0.16rem;
  margin: 0.374rem 0.32rem 0 0;
}

.text-group_4 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.507rem 0 0 0.267rem;
}

.text_32 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.text_33 {
  width: 1.014rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.box_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 17.814rem;
  height: 2.667rem;
  border: 1px solid rgba(211, 211, 211, 1);
  margin-top: 0.027rem;
}

.box_11 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 17.814rem;
  height: 1.6rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-top: 0.534rem;
}

.image-text_15 {
  width: 16.534rem;
  height: 1.067rem;
  margin: 0.24rem 0 0 0.267rem;
}

.thumbnail_4 {
  width: 0.427rem;
  height: 0.427rem;
  margin-top: 0.08rem;
}

.text-group_5 {
  width: 15.84rem;
  height: 1.067rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
}

.text-wrapper_9 {
  background-color: rgba(236, 41, 20, 1);
  height: 1.067rem;
  width: 1.867rem;
  margin: 0.534rem 0 0 7.974rem;
}

.text_34 {
  width: 0.747rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.24rem 0 0 0.56rem;
}

.group_4 {
  width: 0.027rem;
  height: 20.8rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 4.8rem;
}

.image_9 {
  width: 27.467rem;
  height: 1.174rem;
  margin-left: 11.867rem;
}

.box_12 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 6.934rem;
  width: 27.467rem;
  margin: 1.067rem 0 0 11.867rem;
}

.group_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 27.467rem;
  height: 6.934rem;
}

.text_35 {
  width: 3.174rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.694rem 0 0 2.134rem;
}

.text_36 {
  width: 1.467rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.48rem 0 0 2.134rem;
}

.text-wrapper_10 {
  width: 3.574rem;
  height: 1.6rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin: 0.107rem 0 0 2.134rem;
}

.paragraph_1 {
  width: 3.574rem;
  height: 1.6rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
}

.text_37 {
  width: 3.574rem;
  height: 1.6rem;
  overflow-wrap: break-word;
  color: rgba(254, 68, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
}

.text_38 {
  width: 1.467rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.427rem 0 0 2.134rem;
}

.text_39 {
  width: 8.694rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.107rem 0 0.8rem 2.134rem;
}

.box_13 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
  margin: 1.6rem 0 0 11.814rem;
}

.text-wrapper_16 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_40 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_41 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_42 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_17 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_43 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_44 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_45 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_18 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_46 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_19 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_47 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_9 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_16 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_10 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_6 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.294rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}

.box_20 {
  position: absolute;
  left: 11.867rem;
  top: 14.934rem;
  width: 27.467rem;
  height: 3.84rem;
}

.section_3 {
  width: 6.667rem;
  height: 3.84rem;
  background: url(./img/SketchPng1e50148ab5077035a7e6852abbedc692cadf67fe771dfa46252d1d455c39de1b.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_11 {
  width: 1.334rem;
  height: 0.854rem;
  margin: 0.8rem 0 0 2.667rem;
}

.image-text_17 {
  width: 2.027rem;
  height: 0.534rem;
  margin: 0.747rem 0 0.907rem 2.32rem;
}

.text-group_7 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_5 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.187rem;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  width: 6.667rem;
  height: 3.414rem;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.267rem;
}

.label_1 {
  width: 0.854rem;
  height: 0.854rem;
  margin: 0.8rem 0 0 2.907rem;
}

.image-text_18 {
  width: 1.654rem;
  height: 0.534rem;
  margin: 0.747rem 0 0.48rem 2.507rem;
}

.text-group_8 {
  width: 1.12rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_6 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.187rem;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 6.667rem;
  height: 3.414rem;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.267rem;
}

.label_2 {
  width: 0.987rem;
  height: 0.854rem;
  margin: 0.8rem 0 0 2.854rem;
}

.image-text_19 {
  width: 1.28rem;
  height: 0.534rem;
  margin: 0.747rem 0 0.48rem 2.694rem;
}

.text-group_9 {
  width: 0.747rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_7 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.187rem;
}

.section_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 6.667rem;
  height: 3.414rem;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.267rem;
}

.label_3 {
  width: 0.854rem;
  height: 0.854rem;
  margin: 0.8rem 0 0 2.907rem;
}

.image-text_20 {
  width: 2.027rem;
  height: 0.534rem;
  margin: 0.747rem 0 0.48rem 2.32rem;
}

.text-group_10 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_8 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.187rem;
}
