/* ===== 基础样式 ===== */
.page {
  min-height: 100vh;
  background-color: #ffffff;
  font-family: 'AlibabaPuHuiTi-Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== 顶部导航栏 ===== */
.site-header {
  background-color: #ffffff;
  padding: 18px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.header-status {
  position: absolute;
  top: 54px;
  left: 286px;
  color: #ff1d1d;
  font-size: 14px;
  line-height: 20px;
}

.header-content {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 240px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.company-logo {
  width: 56px;
  height: 55px;
}

.company-name {
  font-size: 30px;
  font-family: 'AlibabaPuHuiTi-Medium';
  font-weight: 500;
  color: #000000;
  line-height: 42px;
}

.main-navigation {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  font-size: 18px;
  color: #000000;
  text-decoration: none;
  line-height: 25px;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #0e6be4;
}

.header-actions {
  display: flex;
  align-items: center;
}

.header-icon {
  width: 14px;
  height: 14px;
}

/* ===== 主要内容区域 ===== */
.main-content {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 240px;
}

/* ===== 产品导航区域 ===== */
.product-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 57px;
}

.active-category {
  background-color: #0e6be4;
  box-shadow: -3px -10px 18px rgba(0, 0, 0, 0.14);
  height: 92px;
  width: 118px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  color: #ffffff;
  font-size: 18px;
  line-height: 25px;
}

.category-menu {
  background-color: #090909;
  height: 92px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 50px;
  padding: 0 32px;
  position: relative;
}

.category-link {
  color: #ffffff;
  font-size: 18px;
  line-height: 25px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.category-link:hover {
  color: #0e6be4;
}

.contact-sales-btn {
  background-color: #ffffff;
  height: 92px;
  width: 122px;
  border: none;
  color: #000000;
  font-size: 18px;
  line-height: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.contact-sales-btn:hover {
  background-color: #f5f5f5;
}

.menu-decoration {
  position: absolute;
  left: -84px;
  top: 45px;
  width: 94px;
  height: 1px;
}

/* ===== 主要宣传区域 ===== */
.hero-section {
  width: 100%;
  height: 382px;
  margin-bottom: 57px;
}

.hero-content {
  background-image: url(./img/ddcfe9406c794a5ba905a5b3700a9200_mergeImage.png);
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  border: 1px solid #979797;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 92px 0;
}

.hero-title {
  color: #ffffff;
  font-size: 48px;
  font-family: 'AlibabaPuHuiTi-Bold';
  font-weight: 700;
  line-height: 65px;
  text-align: center;
  margin: 0 0 55px 0;
  max-width: 838px;
}

.cta-button {
  background-color: #ec2914;
  color: #ffffff;
  font-size: 16px;
  line-height: 22px;
  border: none;
  padding: 12px 19px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #d12410;
}

.hero-decoration {
  position: absolute;
  bottom: 19px;
  left: 50%;
  transform: translateX(-50%);
  width: 128px;
  height: 14px;
}

/* ===== 产品预览区域 ===== */
.products-preview {
  margin-bottom: 40px;
}

.section-title {
  font-size: 36px;
  color: #000000;
  line-height: 50px;
  text-align: center;
  margin: 0 0 23px 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 20px;
  align-items: start;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
}

.product-card {
  background-color: #f4f4f4;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-status {
  color: #ff1d1d;
  font-size: 14px;
  line-height: 20px;
}

.card-action-btn {
  background-color: #0e6be4;
  color: #ffffff;
  font-size: 16px;
  line-height: 22px;
  border: none;
  padding: 12px 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.card-action-btn:hover {
  background-color: #0c5bc2;
}

.card-title {
  font-size: 21px;
  color: #000000;
  line-height: 29px;
  margin: 0 0 10px 0;
}

.card-subtitle {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Light';
  font-weight: 300;
  color: #000000;
  line-height: 22px;
  margin: 0;
}

.card-description {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Light';
  font-weight: 300;
  color: #000000;
  line-height: 20px;
  margin: 13px 0 0 0;
}

.card-image, .card-thumbnail {
  width: 30px;
  height: 20px;
  margin-bottom: 10px;
}

.card-label {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Light';
  font-weight: 300;
  color: #000000;
  line-height: 22px;
  text-align: center;
}

/* ===== 服务区域 ===== */
.services-section {
  margin-bottom: 67px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 20px;
  align-items: start;
  justify-content: center;
  max-width: 1200px;
  margin: 52px auto 0;
}

.service-card {
  background-color: #f4f4f4;
  border-radius: 4px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-title {
  font-size: 21px;
  color: #000000;
  line-height: 29px;
  margin: 0 0 12px 0;
}

.service-description {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Light';
  font-weight: 300;
  color: #000000;
  line-height: 20px;
  margin: 0;
}

.service-action-btn {
  background-color: #ec2914;
  color: #ffffff;
  font-size: 16px;
  line-height: 22px;
  border: none;
  padding: 12px 19px;
  cursor: pointer;
  align-self: flex-start;
  transition: background-color 0.3s ease;
  margin-top: 15px;
}

.service-action-btn:hover {
  background-color: #d12410;
}

.config-selector {
  display: flex;
  flex-direction: column;
  gap: 23px;
}

.config-option {
  background-color: #d8d8d8;
  border: 1px solid #979797;
  height: 34px;
  width: 78px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.config-option:hover {
  background-color: #c8c8c8;
}

.config-name {
  font-size: 14px;
  line-height: 20px;
}

/* ===== 公司介绍区域 ===== */
.company-intro {
  text-align: center;
  margin: 67px 0 56px 0;
}

.intro-content {
  max-width: 871px;
  margin: 0 auto;
}

.intro-title {
  font-size: 36px;
  color: #000000;
  line-height: 50px;
  margin: 0 0 15px 0;
}

.intro-description {
  font-size: 20px;
  color: #000000;
  line-height: 27px;
  text-align: right;
  margin: 0;
}

/* ===== 解决方案区域 ===== */
.solutions-section {
  background-color: #000000;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 52px;
}

.solutions-content {
  background-image: url(./img/583d9fa2bf8848e38fcfc6894cd7d9c1_mergeImage.png);
  background-size: cover;
  background-position: center;
  width: 511px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 217px;
}

.solutions-title {
  font-size: 24px;
  font-family: 'PingFangSC-Medium';
  font-weight: 500;
  color: #ffffff;
  line-height: 33px;
  margin: 0;
}

.solutions-cta {
  font-size: 24px;
  font-family: 'PingFangSC-Medium';
  font-weight: 500;
  color: #ffffff;
  line-height: 33px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.solutions-cta:hover {
  opacity: 0.8;
}

/* ===== 新闻动态区域 ===== */
.news-section {
  margin-bottom: 23px;
}

.news-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 23px;
}

.news-title {
  font-size: 36px;
  color: #000000;
  line-height: 50px;
  margin: 0;
}

.view-all-link, .hover-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: #767676;
  transition: color 0.3s ease;
}

.view-all-link:hover, .hover-link:hover {
  color: #0e6be4;
}

.hover-link {
  color: #003a85;
}

.link-text {
  font-size: 14px;
  line-height: 20px;
}

.link-icon {
  width: 14px;
  height: 14px;
}

.hover-status {
  color: #ff1d1d;
  font-size: 14px;
  line-height: 20px;
}

/* ===== 新闻列表区域 ===== */
.news-list {
  margin-bottom: 60px;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  background-color: #f4f4f4;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 338px;
}

.news-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 100%;
  height: 128px;
  background-image: url(./img/e3c271006c954934a5305822e563aafe_mergeImage.png);
  background-size: cover;
  background-position: center;
}

.news-content {
  padding: 10px 20px 95px;
}

.news-headline {
  font-size: 20px;
  color: #000000;
  line-height: 27px;
  margin: 0 0 7px 0;
}

.news-summary {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Light';
  font-weight: 300;
  color: #000000;
  line-height: 22px;
  margin: 0;
}

/* ===== 联系我们区域 ===== */
.contact-section {
  margin-bottom: 86px;
}

.contact-content {
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 100%;
  max-width: 1030px;
  height: 170px;
  margin: 0 auto;
  padding: 26px 50px 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.contact-title {
  font-size: 24px;
  color: #000000;
  line-height: 33px;
  margin: 0;
}

.contact-info {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 120px;
}

.contact-description {
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  margin: 0;
  max-width: 639px;
}

.contact-cta {
  background-color: #ec2914;
  color: #ffffff;
  font-size: 16px;
  line-height: 22px;
  border: none;
  padding: 12px 19px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

.contact-cta:hover {
  background-color: #d12410;
}

/* ===== 页脚区域 ===== */
.site-footer {
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 100%;
  max-width: 1030px;
  height: 230px;
  margin: 0 auto;
  position: relative;
}

.footer-content {
  padding: 18px 60px 3px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.footer-links {
  display: flex;
  gap: 110px;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-heading {
  font-size: 16px;
  font-family: 'AlibabaPuHuiTi-Bold';
  font-weight: 700;
  color: #ffffff;
  line-height: 22px;
  margin: 0;
}

.footer-link {
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #0e6be4;
}

.footer-brand {
  margin: 30px 0 0 20px;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.footer-logo {
  width: 56px;
  height: 55px;
}

.footer-company-name {
  font-size: 30px;
  font-family: 'AlibabaPuHuiTi-Medium';
  font-weight: 500;
  color: #000000;
  line-height: 42px;
}

.footer-gallery {
  display: grid;
  grid-template-columns: repeat(2, 270px);
  grid-template-rows: repeat(2, 166px);
  grid-gap: 3px;
  justify-content: center;
  margin-top: 20px;
}

.gallery-item {
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1440px) {
  .header-content,
  .main-content {
    padding: 0 120px;
  }

  .contact-content,
  .site-footer {
    max-width: 900px;
  }
}

@media (max-width: 1200px) {
  .header-content,
  .main-content {
    padding: 0 60px;
  }

  .products-grid,
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20px;
  }

  .news-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
  }

  .main-navigation {
    gap: 16px;
  }

  .nav-link {
    font-size: 16px;
  }

  .main-content {
    padding: 0 20px;
  }

  .product-navigation {
    flex-direction: column;
    gap: 10px;
  }

  .category-menu {
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .hero-title {
    font-size: 32px;
    line-height: 44px;
  }

  .products-grid,
  .services-grid {
    grid-template-columns: 1fr;
    grid-gap: 20px;
  }

  .news-grid {
    grid-template-columns: 1fr;
  }

  .footer-links {
    flex-direction: column;
    gap: 20px;
  }

  .footer-gallery {
    grid-template-columns: 1fr;
    grid-gap: 10px;
  }

  .contact-info {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .company-name {
    font-size: 24px;
  }

  .hero-title {
    font-size: 24px;
    line-height: 32px;
  }

  .section-title,
  .news-title,
  .intro-title {
    font-size: 28px;
    line-height: 36px;
  }

  .header-status {
    position: static;
    text-align: center;
    margin-bottom: 10px;
  }
}
