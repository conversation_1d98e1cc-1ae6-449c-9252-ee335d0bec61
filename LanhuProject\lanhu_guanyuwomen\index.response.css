.page {
  position: relative;
  width: 100vw;
  height: 212.3vw;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 99.9vw;
  height: 212.3vw;
  margin-left: 0.11vw;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  position: relative;
  width: 100vw;
  height: 27.09vw;
  overflow: hidden;
}

.block_1 {
  height: 54.74vw;
  background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png)
    0vw 13.03vw no-repeat;
  background-size: 82.13vw 27.08vw;
  width: 82.09vw;
  position: absolute;
  left: 8.96vw;
  top: -13.02vw;
}

.section_6 {
  width: 8.96vw;
  height: 2.3vw;
  margin: 14.11vw 0 0 15.31vw;
}

.group_1 {
  border-radius: 50%;
  background-image: url(./img/6002fce2fbf946d3849ae479605d68ed_mergeImage.png);
  width: 2.19vw;
  height: 2.19vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.11vw;
}

.text_1 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
}

.text-wrapper_11 {
  width: 10vw;
  height: 2.5vw;
  margin: 12.81vw 0 0 36.14vw;
}

.text_2 {
  width: 10vw;
  height: 2.5vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 2.5vw;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 3.75vw;
}

.text-wrapper_12 {
  width: 12.35vw;
  height: 1.72vw;
  margin: 0.36vw 0 20.93vw 34.89vw;
}

.text_3 {
  width: 12.35vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  position: absolute;
  left: 23.03vw;
  top: 4.8vw;
  width: 76.98vw;
  height: 4.8vw;
}

.text_4 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text_5 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 3.33vw;
}

.text_6 {
  width: 1.88vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 3.33vw;
}

.text-wrapper_3 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 29.68vw;
}

.text_7 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.61vw 0 0 1.35vw;
}

.block_3 {
  background-color: rgba(255, 255, 255, 1);
  position: absolute;
  left: 35.37vw;
  top: 0;
  width: 64.59vw;
  height: 4.8vw;
}

.text_8 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 1.82vw 0 0 5.62vw;
}

.image_1 {
  width: 3.23vw;
  height: 0.06vw;
  margin: 2.39vw 0 0 0.41vw;
}

.text-wrapper_4 {
  background-color: rgba(14, 107, 228, 1);
  height: 4.8vw;
  margin-left: -0.05vw;
  width: 5.37vw;
}

.text_9 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 1.04vw;
}

.text_10 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 0.98vw;
}

.text_11 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_12 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_13 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.22vw 0 2.08vw;
}

.group_11 {
  position: relative;
  width: 99.9vw;
  height: 185.27vw;
  margin-bottom: 0.06vw;
}

.text_14 {
  width: 45.37vw;
  height: 5.16vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.72vw;
  margin: 2.5vw 0 0 27.23vw;
}

.text_15 {
  width: 7.4vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
  margin: 2.23vw 0 0 46.3vw;
}

.text_16 {
  width: 9.28vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
  margin: 60vw 0 0 45.36vw;
}

.text_17 {
  width: 28.13vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 1.15vw;
  margin: 0.57vw 0 0 36.35vw;
}

.section_7 {
  width: 60.73vw;
  height: 21.62vw;
  margin: 1.66vw 0 0 19.63vw;
}

.box_2 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 19.8vw;
  height: 21.62vw;
}

.image-text_5 {
  width: 17.71vw;
  height: 18.65vw;
  margin: 0.83vw 0 0 1.04vw;
}

.section_4 {
  background-image: url(./img/a7c1fa97577b41ac993c28c674e7202a_mergeImage.png);
  width: 17.71vw;
  height: 9.38vw;
}

.text-group_6 {
  width: 17.71vw;
  height: 8.34vw;
  margin-top: 0.94vw;
}

.text_18 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.paragraph_1 {
  width: 17.71vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.79vw;
}

.box_3 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 19.8vw;
  height: 21.62vw;
  margin-left: 0.84vw;
}

.image-text_6 {
  width: 17.71vw;
  height: 18.65vw;
  margin: 0.83vw 0 0 1.04vw;
}

.group_3 {
  background-image: url(./img/a93624873787473ab557840d124464f1_mergeImage.png);
  width: 17.71vw;
  height: 9.38vw;
}

.text-group_7 {
  width: 17.71vw;
  height: 8.34vw;
  margin-top: 0.94vw;
}

.text_19 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_20 {
  width: 17.71vw;
  height: 6.25vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.79vw;
}

.box_4 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 19.8vw;
  height: 21.62vw;
  margin-left: 0.53vw;
}

.image-text_7 {
  width: 17.71vw;
  height: 14.48vw;
  margin: 0.83vw 0 0 1.04vw;
}

.section_5 {
  background-image: url(./img/8d72b24f0aff4fd98776998cfe0489a4_mergeImage.png);
  width: 17.71vw;
  height: 9.38vw;
}

.text-group_8 {
  width: 17.71vw;
  height: 4.17vw;
  margin-top: 0.94vw;
}

.text_21 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_22 {
  width: 17.71vw;
  height: 2.09vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.79vw;
}

.section_8 {
  width: 4.33vw;
  height: 1.05vw;
  margin: 39.01vw 0 0 27.6vw;
}

.text_23 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_2 {
  width: 0.37vw;
  height: 0.73vw;
  margin-top: 0.21vw;
}

.image-wrapper_1 {
  border-radius: 6px;
  background-image: url(./img/90b2806365c44ec4ae6a3708ca84c279_mergeImage.png);
  height: 26.05vw;
  width: 44.8vw;
  margin: 0.67vw 0 0 27.6vw;
}

.image_2 {
  width: 5.21vw;
  height: 5.21vw;
  margin: 10.41vw 0 0 19.79vw;
}

.group_5 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
  margin: 5.2vw 0 0.05vw 22.96vw;
}

.text-wrapper_13 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_24 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_25 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_26 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_14 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_27 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_28 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_29 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_15 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_30 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_16 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_31 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_12 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_8 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_3 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_4 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.3vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}

.image_4 {
  position: absolute;
  left: 40.79vw;
  top: 74.02vw;
  width: 18.44vw;
  height: 0.21vw;
}

.group_8 {
  position: absolute;
  left: -0.1vw;
  top: 38.29vw;
  width: 100vw;
  height: 31.25vw;
  background: url(./img/SketchPng51cb4f1a65f9f9f2a80566ab0b76c9f8a77b108733ff7084a80bc3c141a63786.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.group_9 {
  background-image: url(./img/89c9827e71bd47e995825799d1464712_mergeImage.png);
  position: absolute;
  left: -0.1vw;
  top: 104.38vw;
  width: 100vw;
  height: 32.82vw;
}

.group_10 {
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 23.18vw;
  top: 13.7vw;
  width: 53.65vw;
  height: 118.7vw;
}

.box_5 {
  border-radius: 8px;
  background-image: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png);
  width: 53.65vw;
  height: 21.46vw;
}

.text-group_9 {
  width: 28.13vw;
  height: 15.16vw;
  margin: 2.44vw 0 0 22.76vw;
}

.text_32 {
  width: 9.28vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
}

.paragraph_2 {
  width: 28.13vw;
  height: 11.46vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin-top: 1.1vw;
}

.text_33 {
  width: 3.7vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
  margin: 6.45vw 0 0 -0.05vw;
}

.box_6 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 20.84vw;
  width: 53.65vw;
  margin: 1.19vw 0 0 -0.1vw;
}

.block_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 53.65vw;
  height: 20.84vw;
}

.text-group_10 {
  width: 28.13vw;
  height: 14.02vw;
  margin: 1.92vw 0 0 2.08vw;
}

.text_34 {
  width: 20.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
}

.paragraph_3 {
  width: 28.13vw;
  height: 10.32vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin-top: 1.1vw;
}

.image_5 {
  width: 18.75vw;
  height: 18.75vw;
  margin: 1.19vw 2.76vw 0 1.92vw;
}

.text_35 {
  width: 18.39vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 2.61vw;
  margin: 40.98vw 0 0 17.55vw;
}

.text-wrapper_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 21.36vw;
  width: 47.92vw;
  margin: 1.19vw 0 0 2.76vw;
}

.paragraph_4 {
  width: 43.75vw;
  height: 18.34vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 1.15vw;
  margin: 1.4vw 0 0 2.08vw;
}

.image_6 {
  position: absolute;
  left: -0.1vw;
  top: 27.87vw;
  width: 2.5vw;
  height: 0.21vw;
}

.image_7 {
  position: absolute;
  left: 38.65vw;
  top: 11.25vw;
  width: 22.71vw;
  height: 0.21vw;
}
