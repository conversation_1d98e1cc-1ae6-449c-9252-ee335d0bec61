.page {
  position: relative;
  width: 100vw;
  height: 109.48vw;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 109.48vw;
}

.box_15 {
  width: 75.68vw;
  height: 4.8vw;
}

.section_1 {
  border-radius: 50%;
  background-image: url(./img/3a51bdb8986a4010a237ffc821e616ff_mergeImage.png);
  width: 2.19vw;
  height: 2.19vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 1.2vw;
}

.text_1 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.09vw 0 0 0.62vw;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 64.59vw;
  height: 4.8vw;
  margin-left: 2.14vw;
}

.text_2 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 13.85vw;
}

.text_3 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.03vw;
}

.text_4 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_5 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.22vw 0 2.08vw;
}

.box_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 76.98vw;
  height: 4.8vw;
  margin-left: 23.03vw;
}

.text_7 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text_8 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 3.33vw;
}

.text_9 {
  width: 1.88vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 3.33vw;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 29.68vw;
}

.text_10 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.61vw 0 0 1.35vw;
}

.box_16 {
  width: 8.86vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 23.95vw;
}

.text_11 {
  width: 4.38vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.thumbnail_2 {
  width: 0.94vw;
  height: 0.94vw;
  margin: 0.05vw 0 0 0.31vw;
}

.text_12 {
  width: 2.92vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 0.32vw;
}

.box_4 {
  background-color: rgba(0, 0, 0, 1);
  width: 100vw;
  height: 10.42vw;
  margin-top: 0.63vw;
}

.group_8 {
  width: 45vw;
  height: 5.32vw;
  margin: 1.92vw 0 0 23.95vw;
}

.image_1 {
  width: 3.65vw;
  height: 3.6vw;
}

.text-wrapper_15 {
  width: 40.32vw;
  height: 5.32vw;
}

.text_13 {
  width: 6.25vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.56vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
}

.text_14 {
  width: 40.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.31vw;
  margin-top: 0.53vw;
}

.box_6 {
  background-color: rgba(255, 67, 0, 1);
  width: 53.65vw;
  height: 0.21vw;
  margin: 2.96vw 0 0 23.17vw;
}

.text-wrapper_3 {
  background-color: rgba(14, 107, 228, 1);
  border-radius: 0px 0px 25px 25px;
  height: 2.61vw;
  width: 53.65vw;
  margin: 2.08vw 0 0 23.17vw;
}

.text_15 {
  width: 5vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 0.46vw 0 0 3.12vw;
}

.box_17 {
  width: 53.65vw;
  height: 40.63vw;
  margin: 8.75vw 0 0 23.17vw;
}

.group_2 {
  width: 0.06vw;
  height: 40.63vw;
  border: 1px solid rgba(0, 0, 0, 1);
}

.box_18 {
  width: 34.8vw;
  height: 36.72vw;
  margin: 2.55vw 0 0 9.37vw;
}

.box_19 {
  width: 18.91vw;
  height: 1.05vw;
  margin-left: 0.53vw;
}

.text-wrapper_4 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_16 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_17 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text-wrapper_5 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_18 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_19 {
  width: 1.25vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-wrapper_3 {
  width: 34.8vw;
  height: 1.78vw;
  margin-top: 0.06vw;
}

.image_2 {
  width: 17.14vw;
  height: 1.78vw;
}

.image_3 {
  width: 17.14vw;
  height: 1.78vw;
}

.text_20 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.image-text_11 {
  width: 34.8vw;
  height: 3.81vw;
  margin-top: 0.06vw;
}

.image_4 {
  width: 34.8vw;
  height: 1.78vw;
}

.text-group_1 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.text_21 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_22 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-text_12 {
  width: 17.14vw;
  height: 3.81vw;
  margin-top: 0.06vw;
}

.image_5 {
  width: 17.14vw;
  height: 1.78vw;
}

.text-group_2 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.text_23 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_24 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-text_13 {
  width: 34.8vw;
  height: 3.81vw;
  margin-top: 0.06vw;
}

.image_6 {
  width: 34.8vw;
  height: 1.78vw;
}

.text-group_11 {
  width: 19.64vw;
  height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.text-wrapper_6 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_25 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_26 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text-wrapper_7 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_27 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_28 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-wrapper_4 {
  width: 34.8vw;
  height: 1.78vw;
  margin-top: 0.06vw;
}

.image_7 {
  width: 17.14vw;
  height: 1.78vw;
}

.image_8 {
  width: 17.14vw;
  height: 1.78vw;
}

.text-wrapper_8 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.text_29 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_30 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.image-text_14 {
  width: 34.8vw;
  height: 3.81vw;
  margin-top: 0.06vw;
}

.box_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 34.8vw;
  height: 1.78vw;
  border: 1px solid rgba(211, 211, 211, 1);
}

.text_31 {
  width: 6.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(211, 211, 211, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.62vw;
}

.thumbnail_3 {
  width: 0.53vw;
  height: 0.32vw;
  margin: 0.72vw 0.62vw 0 0;
}

.text-group_4 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.98vw 0 0 0.52vw;
}

.text_32 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_33 {
  width: 1.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.box_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 34.8vw;
  height: 5.21vw;
  border: 1px solid rgba(211, 211, 211, 1);
  margin-top: 0.06vw;
}

.box_11 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 34.8vw;
  height: 3.13vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-top: 1.05vw;
}

.image-text_15 {
  width: 32.3vw;
  height: 2.09vw;
  margin: 0.46vw 0 0 0.52vw;
}

.thumbnail_4 {
  width: 0.84vw;
  height: 0.84vw;
  margin-top: 0.16vw;
}

.text-group_5 {
  width: 30.94vw;
  height: 2.09vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
}

.text-wrapper_9 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.09vw;
  width: 3.65vw;
  margin: 1.04vw 0 0 15.57vw;
}

.text_34 {
  width: 1.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.46vw 0 0 1.09vw;
}

.group_4 {
  width: 0.06vw;
  height: 40.63vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 9.38vw;
}

.image_9 {
  width: 53.65vw;
  height: 2.3vw;
  margin-left: 23.18vw;
}

.box_12 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 13.55vw;
  width: 53.65vw;
  margin: 2.08vw 0 0 23.17vw;
}

.group_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 53.65vw;
  height: 13.55vw;
}

.text_35 {
  width: 6.2vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.35vw 0 0 4.16vw;
}

.text_36 {
  width: 2.87vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.93vw 0 0 4.16vw;
}

.text-wrapper_10 {
  width: 6.98vw;
  height: 3.13vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin: 0.2vw 0 0 4.16vw;
}

.paragraph_1 {
  width: 6.98vw;
  height: 3.13vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
}

.text_37 {
  width: 6.98vw;
  height: 3.13vw;
  overflow-wrap: break-word;
  color: rgba(254, 68, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
}

.text_38 {
  width: 2.87vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.83vw 0 0 4.16vw;
}

.text_39 {
  width: 16.98vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.2vw 0 1.56vw 4.16vw;
}

.box_13 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
  margin: 3.12vw 0 0 23.07vw;
}

.text-wrapper_16 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_40 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_41 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_42 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_17 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_43 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_44 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_45 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_18 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_46 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_19 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_47 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_9 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_16 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_10 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_6 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.3vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}

.box_20 {
  position: absolute;
  left: 23.18vw;
  top: 29.17vw;
  width: 53.65vw;
  height: 7.5vw;
}

.section_3 {
  width: 13.03vw;
  height: 7.5vw;
  background: url(./img/SketchPng1e50148ab5077035a7e6852abbedc692cadf67fe771dfa46252d1d455c39de1b.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_11 {
  width: 2.61vw;
  height: 1.67vw;
  margin: 1.56vw 0 0 5.2vw;
}

.image-text_17 {
  width: 3.96vw;
  height: 1.05vw;
  margin: 1.45vw 0 1.77vw 4.53vw;
}

.text-group_7 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_5 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.37vw;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  width: 13.03vw;
  height: 6.67vw;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.53vw;
}

.label_1 {
  width: 1.67vw;
  height: 1.67vw;
  margin: 1.56vw 0 0 5.67vw;
}

.image-text_18 {
  width: 3.23vw;
  height: 1.05vw;
  margin: 1.45vw 0 0.93vw 4.89vw;
}

.text-group_8 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_6 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.37vw;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 13.03vw;
  height: 6.67vw;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.53vw;
}

.label_2 {
  width: 1.93vw;
  height: 1.67vw;
  margin: 1.56vw 0 0 5.57vw;
}

.image-text_19 {
  width: 2.5vw;
  height: 1.05vw;
  margin: 1.45vw 0 0.93vw 5.26vw;
}

.text-group_9 {
  width: 1.46vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_7 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.37vw;
}

.section_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 13.03vw;
  height: 6.67vw;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 0.53vw;
}

.label_3 {
  width: 1.67vw;
  height: 1.67vw;
  margin: 1.56vw 0 0 5.67vw;
}

.image-text_20 {
  width: 3.96vw;
  height: 1.05vw;
  margin: 1.45vw 0 0.93vw 4.53vw;
}

.text-group_10 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_8 {
  width: 0.53vw;
  height: 0.32vw;
  margin-top: 0.37vw;
}
