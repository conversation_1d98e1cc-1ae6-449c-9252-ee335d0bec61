html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 52.374rem;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.2rem;
  height: 52.374rem;
}

.group_1 {
  background-image: url(./img/15e8b85ed4654c07b1c1d7c677702da8_mergeImage.png);
  width: 51.2rem;
  height: 13.867rem;
}

.block_1 {
  width: 38.747rem;
  height: 2.454rem;
}

.group_2 {
  border-radius: 50%;
  background-image: url(./img/29d58bed8ff843b5a2cd3433009363bf_mergeImage.png);
  width: 1.12rem;
  height: 1.12rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.614rem;
}

.text_1 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin: 0.56rem 0 0 0.32rem;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  width: 33.067rem;
  height: 2.454rem;
  margin-left: 1.094rem;
}

.text_2 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 7.094rem;
}

.text_3 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.04rem;
}

.text_4 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_5 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.894rem 0 1.067rem;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 39.414rem;
  height: 2.454rem;
  margin-left: 11.787rem;
}

.text_7 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.334rem;
}

.text_8 {
  width: 1.28rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.92rem;
}

.text_9 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.867rem;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.307rem;
}

.text_10 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.88rem 0 0 0.8rem;
}

.text_11 {
  width: 5.12rem;
  height: 1.28rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.28rem;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.92rem;
  margin: 3.387rem 0 0 30.827rem;
}

.text_12 {
  width: 8.214rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.187rem 0 3.227rem 27.734rem;
}

.group_4 {
  width: 51.2rem;
  height: 38.534rem;
  margin-bottom: 0.027rem;
}

.text_13 {
  width: 23.227rem;
  height: 1.76rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.88rem;
  margin: 1.28rem 0 0 14.054rem;
}

.group_5 {
  width: 30.96rem;
  height: 1.227rem;
  margin: 2.187rem 0 0 9.574rem;
}

.group_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 2.507rem;
  height: 1.227rem;
  border: 1px solid rgba(0, 0, 0, 1);
}

.image-text_1 {
  width: 1.867rem;
  height: 0.534rem;
  margin: 0.32rem 0 0 0.32rem;
}

.text-group_1 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_2 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.214rem;
}

.group_7 {
  background-color: rgba(255, 255, 255, 1);
  width: 1.76rem;
  height: 1.227rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 0.694rem;
}

.image-text_2 {
  width: 1.12rem;
  height: 0.534rem;
  margin: 0.32rem 0 0 0.32rem;
}

.text-group_2 {
  width: 0.747rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_3 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.214rem;
}

.group_8 {
  background-color: rgba(255, 255, 255, 1);
  width: 1.76rem;
  height: 1.227rem;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 0.694rem;
}

.image-text_3 {
  width: 1.12rem;
  height: 0.534rem;
  margin: 0.32rem 0 0 0.32rem;
}

.text-group_3 {
  width: 0.747rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_4 {
  width: 0.267rem;
  height: 0.16rem;
  margin-top: 0.214rem;
}

.group_9 {
  box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 1);
  width: 10.827rem;
  height: 1.227rem;
  margin-left: 12.72rem;
}

.text_14 {
  width: 0.747rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(173, 173, 173, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.32rem 0 0 0.32rem;
}

.thumbnail_5 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.427rem 0.427rem 0 8.96rem;
}

.group_10 {
  width: 27.974rem;
  height: 0.587rem;
  margin: 1.6rem 0 0 9.84rem;
}

.image-text_4 {
  width: 2.16rem;
  height: 0.587rem;
}

.text-group_4 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_6 {
  width: 0.214rem;
  height: 0.32rem;
  margin-top: 0.187rem;
}

.image-text_5 {
  width: 1.334rem;
  height: 0.587rem;
  margin-left: 2.134rem;
}

.text-group_5 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_7 {
  width: 0.214rem;
  height: 0.32rem;
  margin-top: 0.187rem;
}

.image-text_6 {
  width: 2.16rem;
  height: 0.587rem;
  margin-left: 2.134rem;
}

.text-group_6 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_8 {
  width: 0.214rem;
  height: 0.32rem;
  margin-top: 0.187rem;
}

.image-text_7 {
  width: 2.16rem;
  height: 0.587rem;
  margin-left: 6.934rem;
}

.text-group_7 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_9 {
  width: 0.214rem;
  height: 0.32rem;
  margin-top: 0.187rem;
}

.image-text_8 {
  width: 1.76rem;
  height: 0.587rem;
  margin-left: 7.2rem;
}

.text-group_8 {
  width: 1.28rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
}

.thumbnail_10 {
  width: 0.214rem;
  height: 0.32rem;
  margin-top: 0.187rem;
}

.group_11 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.32rem 0 0 9.574rem;
}

.text_15 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_16 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 3.414rem;
}

.text_17 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_18 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.094rem;
}

.text_19 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_11 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_12 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_20 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_21 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 3.414rem;
}

.text_22 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_23 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 7.414rem;
}

.text_24 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_12 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_13 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_25 {
  width: 2.107rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_26 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.16rem;
}

.text_27 {
  width: 1.28rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_28 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 7.814rem;
}

.text_29 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_13 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_14 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_30 {
  width: 2.107rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_31 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.16rem;
}

.text_32 {
  width: 3.147rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_33 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 5.947rem;
}

.text_34 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_14 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_15 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_35 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_36 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 3.414rem;
}

.text_37 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_38 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 7.414rem;
}

.text_39 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_15 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_16 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_40 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_41 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 3.414rem;
}

.text_42 {
  width: 4.427rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_43 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 4.667rem;
}

.text_44 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_16 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.group_17 {
  box-shadow: 0px 1px 2px 0px rgba(232, 229, 229, 0.5);
  background-color: rgba(255, 255, 255, 1);
  width: 32.054rem;
  height: 2.614rem;
  margin: 0.027rem 0 0 9.574rem;
}

.text_45 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 0.267rem;
}

.text_46 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 3.414rem;
}

.text_47 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 2.64rem;
}

.text_48 {
  width: 8rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 7.414rem;
}

.text_49 {
  width: 2.96rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.534rem 0 0 1.36rem;
}

.thumbnail_17 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 0.72rem 1.147rem 0 1.094rem;
}

.text-wrapper_2 {
  background-color: rgba(0, 0, 0, 1);
  height: 1.227rem;
  width: 2.667rem;
  margin: 1.067rem 0 0 24.267rem;
}

.text_50 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.267rem 0 0 0.507rem;
}

.group_18 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
  margin: 2.667rem 0 0.027rem 11.814rem;
}

.text-wrapper_3 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_51 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_52 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_53 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_4 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_54 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_55 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_56 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_5 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_57 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_6 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_58 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_19 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_9 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_1 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_9 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.group_20 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.294rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}
