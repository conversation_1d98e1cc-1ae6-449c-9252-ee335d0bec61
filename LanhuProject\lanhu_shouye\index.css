.page {
  position: relative;
  width: 1920px;
  height: 2672px;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1920px;
  height: 2672px;
}

.group_18 {
  width: 1633px;
  height: 92px;
}

.text_1 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 54px;
}

.box_1 {
  border-radius: 50%;
  background-image: url(./img/79b144a48e324afe8d417c938894a984_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin: 23px 0 0 111px;
}

.text_2 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 21px 0 0 12px;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 1240px;
  height: 92px;
  margin-left: 41px;
}

.text_3 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 266px;
}

.text_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 39px;
}

.text_5 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_7 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.group_19 {
  width: 1662px;
  height: 92px;
  margin-left: 258px;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(0, 85, 195, 1);
  height: 92px;
  width: 118px;
}

.text_8 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.box_3 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 1460px;
  height: 92px;
}

.text_9 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_10 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text_11 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 598px;
}

.text_12 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}

.image_1 {
  position: absolute;
  left: -84px;
  top: 45px;
  width: 94px;
  height: 1px;
}

.group_3 {
  background-image: url(./img/aedda77a45764f4a9a84182ecfa9e1d7_mergeImage.png);
  width: 1440px;
  height: 382px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-left: 240px;
}

.text_13 {
  width: 838px;
  height: 65px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 48px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 65px;
  margin: 92px 0 0 292px;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 55px 0 0 670px;
}

.text_14 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image_2 {
  width: 128px;
  height: 14px;
  margin: 91px 0 19px 656px;
}

.group_4 {
  background-image: url(./img/744457cc39614e1b9eb6e9397ab27d32_mergeImage.png);
  height: 680px;
  margin-top: 60px;
  width: 1920px;
}

.text-wrapper_25 {
  width: 119px;
  height: 42px;
  margin: 57px 0 0 901px;
}

.text_15 {
  width: 119px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_26 {
  width: 682px;
  height: 20px;
  margin: 32px 0 0 619px;
}

.text_16 {
  width: 111px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_17 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_18 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_19 {
  width: 83px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_20 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.box_9 {
  width: 105px;
  height: 102px;
  margin: 39px 0 0 1395px;
}

.group_5 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d95c1a527e584dbd8ba4a677be6c22d9_mergeImage.png);
  width: 105px;
  height: 102px;
}

.box_10 {
  width: 105px;
  height: 102px;
  margin: 12px 0 0 1395px;
}

.group_6 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d84667fe1ad94a8290dd55bdfb3ae3be_mergeImage.png);
  width: 105px;
  height: 102px;
}

.box_11 {
  width: 1061px;
  height: 102px;
  margin: 12px 0 0 439px;
}

.text_21 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 75px;
}

.box_4 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/5fa0fd3476574832a0fa7dc2e79e889a_mergeImage.png);
  width: 105px;
  height: 102px;
}

.box_12 {
  width: 1064px;
  height: 107px;
  margin: 2px 0 51px 436px;
}

.text-wrapper_6 {
  background-color: rgba(0, 0, 0, 1);
  height: 34px;
  width: 78px;
}

.text_22 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.image_3 {
  width: 71px;
  height: 53px;
  margin: 25px 0 0 16px;
}

.text-wrapper_7 {
  background-color: rgba(0, 0, 0, 0);
  height: 34px;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 78px;
  margin: 64px 0 0 -1px;
}

.text_23 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-wrapper_8 {
  background-color: rgba(0, 0, 0, 0);
  height: 34px;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 78px;
  margin: 64px 0 0 40px;
}

.text_24 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-group_16 {
  width: 64px;
  height: 48px;
  margin: 59px 0 0 252px;
}

.text_25 {
  width: 64px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_26 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 9px;
}

.group_7 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_17 {
  width: 59px;
  height: 48px;
  margin: 59px 0 0 30px;
}

.text_27 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_28 {
  width: 59px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 10px;
}

.group_8 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 31px;
}

.text-group_18 {
  width: 62px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_9 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_29 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_30 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_31 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.group_9 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_19 {
  width: 83px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_10 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_32 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_33 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_34 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.text-group_20 {
  width: 871px;
  height: 119px;
  margin: 57px 0 0 513px;
}

.text_35 {
  width: 355px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin-left: 270px;
}

.text_36 {
  width: 871px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 27px;
  margin-top: 15px;
}

.group_10 {
  background-color: rgba(0, 0, 0, 1);
  width: 1030px;
  height: 120px;
  margin: 56px 0 0 433px;
}

.text_37 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 206px;
}

.text-wrapper_11 {
  background-image: url(./img/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
  height: 120px;
  margin-left: 217px;
  width: 511px;
}

.text_38 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 208px;
}

.group_20 {
  width: 1140px;
  height: 50px;
  margin: 57px 0 0 510px;
}

.text_39 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
}

.image-text_8 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 725px;
}

.text-group_6 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_2 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.image_4 {
  width: 77px;
  height: 1px;
  margin: 32px 0 0 20px;
}

.image-text_9 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 16px;
}

.text-group_7 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_3 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.list_3 {
  width: 1030px;
  height: 338px;
  justify-content: space-between;
  margin: 23px 0 0 433px;
}

.image-text_3-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_12-0 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
}

.text-group_21-0 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_40-0 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_41-0 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_12-1 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/6e327735808b4c35be47442543b0094a_mergeImage.png);
}

.text-group_21-1 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_40-1 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_41-1 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_12-2 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/050735b55ad74444aa753f87269e74f6_mergeImage.png);
}

.text-group_21-2 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_40-2 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_41-2 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_12-3 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(./img/a40c4aded9fe41128fc2d24e4588ad00_mergeImage.png);
}

.text-group_21-3 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_40-3 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 27px;
}

.text_41-3 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.group_13 {
  height: 170px;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  margin: 60px 0 0 433px;
}

.text-wrapper_27 {
  width: 95px;
  height: 33px;
  margin: 26px 0 0 50px;
}

.text_42 {
  width: 95px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.group_21 {
  width: 859px;
  height: 48px;
  margin: 3px 0 60px 50px;
}

.text_43 {
  width: 639px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin-top: 8px;
}

.text-wrapper_13 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
}

.text_44 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.group_15 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 86px 0 0 433px;
}

.text-wrapper_28 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_45 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_46 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_47 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_29 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_48 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_49 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_50 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_30 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_51 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_31 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_52 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_22 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_10 {
  width: 178px;
  height: 55px;
}

.image_5 {
  width: 56px;
  height: 55px;
}

.text-group_9 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.group_17 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 246px;
  top: 170px;
  width: 1241px;
  height: 60px;
}
